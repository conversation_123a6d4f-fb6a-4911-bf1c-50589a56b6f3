import React from 'react';
import { getUserTimezone, getTimezoneOffset, validateTimezone } from '../../utils/timezoneUtils';

class TimezoneErrorBoundary extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            hasError: false,
            errorInfo: null,
            timezoneSupported: true
        };
    }

    static getDerivedStateFromError(error) {
        // Check if this is a timezone-related error
        const isTimezoneError = error.message?.includes('timezone') ||
            error.message?.includes('Intl') ||
            error.message?.includes('locale');

        return {
            hasError: true,
            isTimezoneError
        };
    }

    componentDidCatch(error, errorInfo) {
        console.error('Timezone Error Boundary caught an error:', error, errorInfo);

        // Log timezone environment info
        try {
            const timezone = getUserTimezone();
            const offset = getTimezoneOffset();
            const isSupported = validateTimezone(timezone);

            console.log('Timezone Environment:', {
                userTimezone: timezone,
                timezoneOffset: offset,
                timezoneSupported: isSupported,
                browserSupport: {
                    Intl: typeof Intl !== 'undefined',
                    DateTimeFormat: typeof Intl?.DateTimeFormat !== 'undefined',
                    timeZone: typeof Intl?.DateTimeFormat()?.resolvedOptions !== 'undefined'
                }
            });
        } catch (e) {
            console.error('Failed to log timezone environment:', e);
        }

        this.setState({
            errorInfo,
            timezoneSupported: this.checkTimezoneSupport()
        });
    }

    checkTimezoneSupport() {
        try {
            // Test basic Intl support
            if (typeof Intl === 'undefined') return false;
            if (typeof Intl.DateTimeFormat === 'undefined') return false;

            // Test timezone support
            const testDate = new Date();
            testDate.toLocaleString('en-US', { timeZone: 'America/New_York' });

            return true;
        } catch (e) {
            return false;
        }
    }

    handleRetry = () => {
        this.setState({ hasError: false, errorInfo: null });
    };

    render() {
        if (this.state.hasError) {
            return (
                <div className="timezone-error-boundary">
                    <div className="error-card">
                        <h2>🌍 Timezone Error</h2>

                        {!this.state.timezoneSupported ? (
                            <div className="browser-support-error">
                                <p>Your browser doesn't fully support timezone functionality.</p>
                                <div className="suggestions">
                                    <h4>Suggestions:</h4>
                                    <ul>
                                        <li>Update your browser to the latest version</li>
                                        <li>Try using Chrome, Firefox, or Safari</li>
                                        <li>Enable JavaScript if it's disabled</li>
                                    </ul>
                                </div>
                            </div>
                        ) : (
                            <div className="timezone-error-details">
                                <p>There was an issue with date/timezone handling.</p>
                                <details className="error-details">
                                    <summary>Technical Details</summary>
                                    <div className="timezone-info">
                                        <p><strong>Your Timezone:</strong> {getUserTimezone()}</p>
                                        <p><strong>UTC Offset:</strong> {getTimezoneOffset()}</p>
                                        <p><strong>Browser:</strong> {navigator.userAgent}</p>
                                    </div>
                                    {this.state.errorInfo && (
                                        <pre className="error-stack">
                                            {this.state.errorInfo.componentStack}
                                        </pre>
                                    )}
                                </details>
                            </div>
                        )}

                        <div className="error-actions">
                            <button
                                className="btn btn-primary"
                                onClick={this.handleRetry}
                            >
                                Try Again
                            </button>
                            <button
                                className="btn btn-outline"
                                onClick={() => window.location.reload()}
                            >
                                Refresh Page
                            </button>
                        </div>
                    </div>

                    <style jsx>{`
            .timezone-error-boundary {
              display: flex;
              justify-content: center;
              align-items: center;
              min-height: 300px;
              padding: 20px;
            }
            
            .error-card {
              background: #fff;
              border: 1px solid #ddd;
              border-radius: 8px;
              padding: 24px;
              max-width: 600px;
              box-shadow: 0 2px 10px rgba(0,0,0,0.1);
              text-align: center;
            }
            
            .error-card h2 {
              color: #d32f2f;
              margin-bottom: 16px;
            }
            
            .suggestions ul {
              text-align: left;
              margin: 12px 0;
            }
            
            .error-details {
              text-align: left;
              margin: 16px 0;
            }
            
            .timezone-info {
              background: #f5f5f5;
              padding: 12px;
              border-radius: 4px;
              margin: 8px 0;
            }
            
            .error-stack {
              background: #f5f5f5;
              padding: 12px;
              border-radius: 4px;
              overflow-x: auto;
              font-size: 12px;
              margin: 8px 0;
            }
            
            .error-actions {
              margin-top: 20px;
              display: flex;
              gap: 12px;
              justify-content: center;
            }
            
            .btn {
              padding: 8px 16px;
              border-radius: 4px;
              border: none;
              cursor: pointer;
              font-weight: 500;
            }
            
            .btn-primary {
              background: #1976d2;
              color: white;
            }
            
            .btn-outline {
              background: transparent;
              color: #1976d2;
              border: 1px solid #1976d2;
            }
            
            .btn:hover {
              opacity: 0.9;
            }
          `}</style>
                </div>
            );
        }

        return this.props.children;
    }
}

export default TimezoneErrorBoundary; 