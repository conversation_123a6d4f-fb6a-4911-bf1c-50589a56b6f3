const fs = require('fs');
const path = require('path');
const mammoth = require('mammoth');
const XLSX = require('xlsx');

/**
 * Enhanced Document Converter Service
 * Converts Office documents to HTML/JSON for preview without external services
 * Improved with better PowerPoint support and error handling
 */

/**
 * Convert Word document to HTML
 * @param {string} filePath - Path to the Word document
 * @param {boolean} firstPageOnly - Whether to extract only the first page content
 * @returns {Promise<Object>} - Converted HTML content and metadata
 */
const convertWordToHtml = async (filePath, firstPageOnly = false) => {
  try {
    console.log(`[DocumentConverter] Converting Word document: ${filePath} (firstPageOnly: ${firstPageOnly})`);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      throw new Error(`Word document not found: ${filePath}`);
    }

    // Convert using mammoth with enhanced options
    const options = {
      convertImage: mammoth.images.imgElement(function (image) {
        // Convert images to base64 for self-contained preview
        return image.read("base64").then(function (imageBuffer) {
          return {
            src: "data:" + image.contentType + ";base64," + imageBuffer
          };
        });
      }),
      styleMap: [
        "p[style-name='Heading 1'] => h1:fresh",
        "p[style-name='Heading 2'] => h2:fresh",
        "p[style-name='Heading 3'] => h3:fresh",
        "p[style-name='Title'] => h1.title",
        "r[style-name='Strong'] => strong",
        "r[style-name='Emphasis'] => em"
      ]
    };

    const result = await mammoth.convertToHtml({ path: filePath }, options);
    const textResult = await mammoth.extractRawText({ path: filePath });

    let htmlContent = result.value;
    let textContent = textResult.value;

    // Clean up HTML content
    htmlContent = htmlContent
      .replace(/<p><\/p>/g, '') // Remove empty paragraphs
      .replace(/(<p[^>]*>)\s*(<\/p>)/g, '') // Remove whitespace-only paragraphs
      .trim();

    // If first page only, truncate content intelligently
    if (firstPageOnly) {
      const paragraphs = htmlContent.split('</p>');
      const targetLength = Math.min(15, paragraphs.length); // Limit to ~15 paragraphs
      const firstPageParagraphs = paragraphs.slice(0, targetLength);
      htmlContent = firstPageParagraphs.join('</p>') + (firstPageParagraphs.length < paragraphs.length ? '</p>' : '');

      // Add preview notice if content was truncated
      if (firstPageParagraphs.length < paragraphs.length) {
        htmlContent += '<div class="preview-notice" style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-left: 4px solid #007bff; font-style: italic; border-radius: 4px;">📄 This is a preview showing the first page. Purchase to view the complete document with all content, formatting, and images.</div>';
      }

      // Truncate text content similarly
      const textParagraphs = textContent.split('\n\n');
      textContent = textParagraphs.slice(0, targetLength).join('\n\n');
    }

    const wordCount = textContent.split(/\s+/).filter(word => word.length > 0).length;
    const charCount = textContent.length;
    const estimatedPages = Math.ceil(wordCount / 250); // Rough estimate: 250 words per page

    console.log(`[DocumentConverter] Word conversion successful. Words: ${wordCount}, Characters: ${charCount}, EstimatedPages: ${estimatedPages}, FirstPageOnly: ${firstPageOnly}`);

    return {
      success: true,
      html: htmlContent,
      text: textContent,
      metadata: {
        wordCount,
        charCount,
        estimatedPages,
        hasImages: htmlContent.includes('<img'),
        warnings: result.messages || [],
        isPreview: firstPageOnly,
        documentType: 'word'
      }
    };
  } catch (error) {
    console.error(`[DocumentConverter] Word conversion failed:`, error);
    return {
      success: false,
      error: error.message,
      html: null,
      text: null,
      metadata: {
        documentType: 'word',
        isPreview: false,
        error: true
      }
    };
  }
};

/**
 * Convert Excel document to structured data
 * @param {string} filePath - Path to the Excel document
 * @param {boolean} firstSheetOnly - Whether to extract only the first sheet content
 * @returns {Promise<Object>} - Converted data and metadata
 */
const convertExcelToData = async (filePath, firstSheetOnly = false) => {
  try {
    console.log(`[DocumentConverter] Converting Excel document: ${filePath} (firstSheetOnly: ${firstSheetOnly})`);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      throw new Error(`Excel document not found: ${filePath}`);
    }

    // Read the workbook with enhanced options
    const workbook = XLSX.readFile(filePath, {
      cellText: true,
      cellDates: true,
      cellNF: false,
      cellStyles: true
    });

    const sheetNames = workbook.SheetNames;

    const sheets = {};
    let totalRows = 0;
    let totalCells = 0;

    // Determine which sheets to process
    const sheetsToProcess = firstSheetOnly ? [sheetNames[0]] : sheetNames.slice(0, 5); // Limit to 5 sheets max

    // Convert each sheet
    sheetsToProcess.forEach(sheetName => {
      const worksheet = workbook.Sheets[sheetName];

      // Get range to determine actual data bounds
      const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1:A1');

      // Convert to JSON with headers
      const jsonData = XLSX.utils.sheet_to_json(worksheet, {
        header: 1,
        defval: '',
        raw: false,
        dateNF: 'yyyy-mm-dd'
      });

      // Filter out completely empty rows
      const filteredData = jsonData.filter(row =>
        row.some(cell => cell !== null && cell !== undefined && cell !== '')
      );

      // Limit rows for preview
      const maxRows = firstSheetOnly ? 25 : 15;
      const previewData = filteredData.slice(0, maxRows);

      // Limit columns to prevent overwhelming display
      const maxCols = 10;
      const limitedData = previewData.map(row => row.slice(0, maxCols));

      sheets[sheetName] = {
        data: limitedData,
        rowCount: filteredData.length,
        columnCount: Math.min(Math.max(...filteredData.map(row => row.length), 0), maxCols),
        isPreview: firstSheetOnly && (filteredData.length > maxRows || Math.max(...filteredData.map(row => row.length), 0) > maxCols),
        range: worksheet['!ref'] || 'A1:A1'
      };

      totalRows += filteredData.length;
      totalCells += filteredData.reduce((sum, row) => sum + row.length, 0);
    });

    // Add preview notice for truncated content
    if (firstSheetOnly && (totalRows > 25 || Object.keys(sheets).length > 1)) {
      sheets['_previewNotice'] = {
        message: '📊 This is a preview showing limited rows and columns. Purchase to access the complete spreadsheet with all data and worksheets.',
        isNotice: true
      };
    }

    console.log(`[DocumentConverter] Excel conversion successful. Sheets: ${Object.keys(sheets).length}, TotalRows: ${totalRows}, TotalCells: ${totalCells}, FirstSheetOnly: ${firstSheetOnly}`);

    return {
      success: true,
      sheets: sheets,
      metadata: {
        sheetCount: sheetNames.length,
        totalRows,
        totalCells,
        sheetNames: sheetNames,
        isPreview: firstSheetOnly,
        documentType: 'excel'
      }
    };
  } catch (error) {
    console.error(`[DocumentConverter] Excel conversion failed:`, error);
    return {
      success: false,
      error: error.message,
      sheets: null,
      metadata: {
        documentType: 'excel',
        isPreview: false,
        error: true
      }
    };
  }
};

/**
 * Main document conversion function
 * @param {string} filePath - Path to the document
 * @param {string} fileExtension - File extension (.docx, .xlsx, etc.)
 * @param {boolean} firstPageOnly - Whether to extract only the first page/sheet/slide
 * @returns {Promise<Object>} - Conversion result
 */
const convertDocument = async (filePath, fileExtension, firstPageOnly = false) => {
  const ext = fileExtension.toLowerCase();

  console.log(`[DocumentConverter] Starting conversion for ${ext} document: ${filePath} (firstPageOnly: ${firstPageOnly})`);

  try {
    switch (ext) {
      case '.doc':
      case '.docx':
      case '.docm':
      case '.dot':
      case '.dotx':
      case '.dotm':
        return await convertWordToHtml(filePath, firstPageOnly);

      case '.xls':
      case '.xlsx':
      case '.xlsm':
      case '.xlt':
      case '.xltx':
      case '.xltm':
      case '.csv':
        return await convertExcelToData(filePath, firstPageOnly);

      default:
        console.log(`[DocumentConverter] Unsupported file type: ${ext}`);
        return {
          success: false,
          error: `Unsupported document type: ${ext}`,
          supportedTypes: ['.docx', '.xlsx', '.doc', '.xls']
        };
    }
  } catch (error) {
    console.error(`[DocumentConverter] Conversion failed for ${ext}:`, error);
    return {
      success: false,
      error: `Document conversion failed: ${error.message}`,
      filePath,
      fileExtension: ext
    };
  }
};

/**
 * Check if document type is supported for conversion
 * @param {string} fileExtension - File extension
 * @returns {boolean} - Whether the type is supported
 */
const isSupportedDocumentType = (fileExtension) => {
  const supportedTypes = [
    '.doc', '.docx', '.docm', '.dot', '.dotx', '.dotm',
    '.xls', '.xlsx', '.xlsm', '.xlt', '.xltx', '.xltm', '.csv'
  ];

  return supportedTypes.includes(fileExtension.toLowerCase());
};

/**
 * Get document type category
 * @param {string} fileExtension - File extension
 * @returns {string} - Document type (word, excel, unknown)
 */
const getDocumentType = (fileExtension) => {
  const ext = fileExtension.toLowerCase();

  if (['.doc', '.docx', '.docm', '.dot', '.dotx', '.dotm'].includes(ext)) {
    return 'word';
  }

  if (['.xls', '.xlsx', '.xlsm', '.xlt', '.xltx', '.xltm', '.csv'].includes(ext)) {
    return 'excel';
  }

  return 'unknown';
};

/**
 * Generate preview for Word document (first page only)
 * @param {string} filePath - Path to the Word document
 * @returns {Promise<Object>} - Preview conversion result
 */
const generateWordPreview = async (filePath) => {
  return await convertWordToHtml(filePath, true);
};

/**
 * Generate preview for Excel document (first sheet only)
 * @param {string} filePath - Path to the Excel document
 * @returns {Promise<Object>} - Preview conversion result
 */
const generateExcelPreview = async (filePath) => {
  return await convertExcelToData(filePath, true);
};

/**
 * Generate document preview based on file extension
 * @param {string} filePath - Path to the document
 * @param {string} fileExtension - File extension
 * @returns {Promise<Object>} - Preview conversion result
 */
const generateDocumentPreview = async (filePath, fileExtension) => {
  return await convertDocument(filePath, fileExtension, true);
};

module.exports = {
  convertDocument,
  convertWordToHtml,
  convertExcelToData,
  generateWordPreview,
  generateExcelPreview,
  generateDocumentPreview,
  isSupportedDocumentType,
  getDocumentType
};
