import React, { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useSelector } from "react-redux";
import { FaMobile } from "react-icons/fa6";
import { toast } from "react-toastify";
import "../../styles/CheckoutPage.css";
import { formatWithTimezone } from '../../utils/timezoneUtils';

const CheckoutPage = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { user } = useSelector((state) => state.auth);

  const [formData, setFormData] = useState({
    phone: "",
    countryCode: "+91",
  });

  const [errors, setErrors] = useState({});
  const [showVerificationModal, setShowVerificationModal] = useState(false);
  const [verificationCode, setVerificationCode] = useState(["", "", "", "", "", ""]);
  const [verificationError, setVerificationError] = useState("");
  const [isVerified, setIsVerified] = useState(false);

  // Check if user is already logged in and redirect to proper checkout
  useEffect(() => {
    if (user) {
      // Check effective role for non-admin users
      const effectiveRole = user.role === 'admin' ? user.role : (user.activeRole || user.role);
      if (effectiveRole === 'buyer' || user.role === 'admin') {
        const contentId = searchParams.get('contentId');
        if (contentId) {
          // User is logged in, redirect to content detail to create order
          navigate(`/buyer/details/${contentId}`);
        } else {
          // No content ID, redirect to dashboard
          navigate('/buyer/dashboard');
        }
      }
    }
  }, [user, navigate, searchParams]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    });

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null,
      });
    }
  };

  const handleCountryCodeChange = (e) => {
    setFormData({
      ...formData,
      countryCode: e.target.value,
    });
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.phone.trim()) {
      newErrors.phone = "Phone number is required";
    } else if (!/^\d{10}$/.test(formData.phone)) {
      newErrors.phone = "Phone number must be 10 digits";
    }

    return newErrors;
  };

  const handleVerificationCodeChange = (index, value) => {
    if (value.length > 1) return; // Only allow single digit

    const newCode = [...verificationCode];
    newCode[index] = value;
    setVerificationCode(newCode);

    // Auto-focus next input
    if (value && index < 5) {
      const nextInput = document.getElementById(`code-${index + 1}`);
      if (nextInput) nextInput.focus();
    }

    // Clear error when user starts typing
    if (verificationError) {
      setVerificationError("");
    }
  };

  const handleVerificationKeyDown = (index, e) => {
    // Handle backspace to go to previous input
    if (e.key === "Backspace" && !verificationCode[index] && index > 0) {
      const prevInput = document.getElementById(`code-${index - 1}`);
      if (prevInput) prevInput.focus();
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    // Show verification modal
    setShowVerificationModal(true);
    console.log("Verification code sent to:", formData.countryCode + formData.phone);
  };

  const handleVerificationSubmit = () => {
    const code = verificationCode.join("");

    if (code.length !== 6) {
      setVerificationError("Please enter the complete verification code");
      return;
    }

    // Simulate verification (in real app, this would be an API call)
    console.log("Verifying code:", code);

    // For demo purposes, accept any 6-digit code
    if (code.length === 6) {
      setShowVerificationModal(false);
      setVerificationCode(["", "", "", "", "", ""]);
      setVerificationError("");
      setIsVerified(true);
      console.log("Verification successful! Proceeding to checkout...");
    } else {
      setVerificationError("Invalid verification code");
    }
  };

  const handleBackToPage = () => {
    setShowVerificationModal(false);
    setVerificationCode(["", "", "", "", "", ""]);
    setVerificationError("");
  };

  const handlePlaceOrder = () => {
    // Create mock order data to pass to thank you page
    const orderData = {
      orderId: "#" + Math.random().toString(36).substring(2, 10).toUpperCase(),
      date: formatWithTimezone(new Date(), {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
      time: formatWithTimezone(new Date(), {
        hour: '2-digit',
        minute: '2-digit',
        timeZoneName: 'short'
      }),
      items: 1,
      totalAmount: "$22.00",
      customerDetails: {
        name: "John Smith",
        email: "<EMAIL>",
        phone: formData.countryCode + " " + formData.phone,
      },
      paymentDetails: {
        cardNumber: "•••• •••• •••• 1234",
        method: "Mastercard",
      },
      itemInfo: {
        title: "Frank Martin - Drills and Coaching Philosophy to Developing Toughness in Basketball",
        category: "Basketball",
        image: "https://via.placeholder.com/80x80/f5f5f5/666666?text=IMG",
      },
    };

    // Navigate to thank you page with order data
    navigate("/thank-you", { state: { orderData } });
  };
  return (
    <div className="checkout-page">
      {/* Main Content */}
      <div className="max-container">
        <div className="checkout-content">
          {/* Left Section - Checkout Form */}
          {!isVerified ? (
            <div className="checkout-left">
              <div className="checkout-form-container">
                <h1 className="checkout-title">Checkout</h1>

                <div className="leftborderdiv">
                  {/* Alert Message */}
                  <div className="checkout-alert">
                    Please log in or sign up to purchase content!
                  </div>

                  {/* Sign In Section */}
                  <div className="signin-section">
                    <div className="signin-sectioncontainer">
                      <h2 className="signin-title">Sign In</h2>
                      <p className="signin-subtitle">
                        Don't have an account?{" "}
                        <a href="/signup" className="signup-link">
                          Sign Up
                        </a>
                      </p>
                    </div>

                    <form onSubmit={handleSubmit} className="signin-form">
                      <div className="auth-form-input form-input-container">
                        <div className="phone-input-wrapper">
                          <div>
                            <div className="country-code-select">
                              <FaMobile style={{ color: "var(--dark-gray)" }} />
                              <select
                                value={formData.countryCode}
                                onChange={handleCountryCodeChange}
                                className="selectstylesnone"
                              >
                                <option value="+91">+91</option>
                                <option value="+1">+1</option>
                                <option value="+44">+44</option>
                                <option value="+61">+61</option>
                                <option value="+86">+86</option>
                                <option value="+49">+49</option>
                                <option value="+33">+33</option>
                                <option value="+81">+81</option>
                                <option value="+7">+7</option>
                                <option value="+55">+55</option>
                              </select>
                            </div>
                          </div>
                          <input
                            type="tel"
                            id="phone"
                            name="phone"
                            value={formData.phone}
                            onChange={(e) => {
                              const phoneValue = e.target.value.replace(/\D/g, "");
                              handleChange({
                                target: {
                                  name: "phone",
                                  value: phoneValue,
                                },
                              });
                            }}
                            placeholder="Enter Phone Number"
                            className={`form-input phone-input ${errors.phone ? "input-error" : ""
                              }`}
                            required
                            pattern="[0-9]*"
                          />
                        </div>
                        {errors.phone && <p className="error-message">{errors.phone}</p>}
                      </div>

                      <button type="submit" className="btn-primary verifybtnwidth">
                        Send Verification Code
                      </button>
                    </form>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="checkout-left">
              <div className="checkout-form-container">
                <h1 className="checkout-title">Checkout</h1>

                <div className="leftborderdiv">
                  {/* Success Message */}
                  <div className="checkout-success">
                    ✅ Phone number verified successfully!
                  </div>

                  {/* Checkout Content */}
                  <div className="checkout-section">
                    <h2 className="section-title">Saved Cards</h2>

                    {/* Sample saved cards */}
                    <div className="saved-cards">
                      <div className="card-option">
                        <input type="radio" id="card1" name="payment" defaultChecked />
                        <label htmlFor="card1" className="card-label">
                          <div className="card-info">
                            <span className="card-type">Mastercard</span>
                            <span className="card-number">•••• •••• •••• 1234</span>
                          </div>
                        </label>
                      </div>

                      <div className="card-option">
                        <input type="radio" id="card2" name="payment" />
                        <label htmlFor="card2" className="card-label">
                          <div className="card-info">
                            <span className="card-type">Mastercard</span>
                            <span className="card-number">•••• •••• •••• 1234</span>
                          </div>
                        </label>
                      </div>

                      <div className="card-option">
                        <input type="radio" id="card3" name="payment" />
                        <label htmlFor="card3" className="card-label">
                          <div className="card-info">
                            <span className="card-type">Mastercard</span>
                            <span className="card-number">•••• •••• •••• 1234</span>
                          </div>
                        </label>
                      </div>
                    </div>

                    <h3 className="section-title">Payment Method</h3>

                    {/* Payment form */}
                    <div className="payment-form">
                      <div className="form-row">
                        <input
                          type="text"
                          placeholder="Name on card"
                          className="form-input"
                        />
                        <input
                          type="text"
                          placeholder="Card Number"
                          className="form-input"
                        />
                      </div>

                      <div className="form-row">
                        <input
                          type="text"
                          placeholder="Exp. Date"
                          className="form-input"
                        />
                        <input
                          type="text"
                          placeholder="CVV"
                          className="form-input"
                        />
                      </div>
                    </div>

                    <button className="btn-primary place-order-main-btn" onClick={handlePlaceOrder}>
                      Place Order
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Right Section - Order Summary */}
          <div className="checkout-right">
            <div className="order-summary">
              <h2 className="order-title">Your Order</h2>

              <div className="rightbackgrounddiv">
                {/* Item Info */}
                <div className="item-info-section">
                  <h3 className="item-info-title">Item Info</h3>

                  <div className="item-details">
                    <div className="item-image">
                      <img
                        src="https://via.placeholder.com/80x80/f5f5f5/666666?text=IMG"
                        alt=""
                        className="item-thumbnail"
                      />
                    </div>

                    <div className="item-description">
                      <h4 className="item-name">
                        Frank Martin - Drills and Coaching Philosophy to
                        Developing Toughness in Basketball
                      </h4>
                    </div>
                  </div>
                </div>

                {/* Pricing */}
                <div className="pricing-section">
                  <div className="price-row">
                    <span className="price-label">Subtotal</span>
                    <span className="price-value">$22.00</span>
                  </div>

                  <div className="price-row total-row">
                    <span className="price-label">Total</span>
                    <span className="price-value">$22.00</span>
                  </div>
                </div>

                {/* Place Order Button */}
                <button className="place-order-btn btn-primary" onClick={handlePlaceOrder}>
                  Place Order & Download
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Code Verification Modal */}
      {showVerificationModal && (
        <div className="verification-modal-overlay">
          <div className="verification-modal">
            <div className="verification-modal-content">
              <h2 className="verification-title">Code Verification</h2>
              <p className="verification-subtitle">
                Enter the Code Sent To {formData.countryCode} {formData.phone.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3')}
              </p>

              <div className="verification-code-container">
                {verificationCode.map((digit, index) => (
                  <input
                    key={index}
                    id={`code-${index}`}
                    type="text"
                    value={digit}
                    onChange={(e) => handleVerificationCodeChange(index, e.target.value)}
                    onKeyDown={(e) => handleVerificationKeyDown(index, e)}
                    className="verification-code-input"
                    maxLength="1"
                    pattern="[0-9]*"
                    inputMode="numeric"
                  />
                ))}
              </div>

              {verificationError && (
                <p className="verification-error">{verificationError}</p>
              )}

              <p className="verification-resend">
                Didn't Received Any Code? <span className="resend-link">Resend</span>
              </p>

              <div className="verification-buttons">
                <button
                  onClick={handleVerificationSubmit}
                  className="btn-primary verification-submit-btn"
                >
                  Verify
                </button>

                <button
                  onClick={handleBackToPage}
                  className="verification-back-btn"
                >
                  Back To Page
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CheckoutPage;
