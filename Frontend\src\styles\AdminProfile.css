.AdminProfile {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.AdminProfile h2 {
  color: #2c3e50;
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 2rem;
  padding: 0.5rem 1rem;
  background-color: #fce7e7;
  border-radius: 8px;
  display: inline-block;
}

.AdminProfile__content {
  background: #fff;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 3rem;
}

.AdminProfile__form {
  width: 100%;
  display: flex;
  gap: 3rem;
}

.AdminProfile__fields {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.AdminProfile__field-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.AdminProfile__field-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #666;
  font-weight: 500;
}

.AdminProfile__field-group input {
  padding: 0.75rem 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  width: 100%;
  background: #f8f9fa;
  color: #2c3e50;
}

.AdminProfile__field-group input:disabled {
  background: #f8f9fa;
  color: #666;
  cursor: not-allowed;
}

.AdminProfile__field-group input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.1);
}

.AdminProfile__image-section {
  flex: 0 0 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.AdminProfile__image-container {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  overflow: hidden;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.AdminProfile__image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.AdminProfile__default-avatar {
  font-size: 4rem;
  color: #ccc;
}

.AdminProfile__upload-btn {
  background: #fff;
  color: #ff385c;
  border: 1px solid #ff385c;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.AdminProfile__upload-btn:hover {
  background: #ff385c;
  color: #fff;
}

.AdminProfile__save-btn {
  background: #ff385c;
  color: #fff;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  margin-top: 1rem;
  width: fit-content;
}

.AdminProfile__save-btn:hover {
  background: #e6324f;
}

.AdminProfile__save-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* Loading state */
.AdminProfile__save-btn.loading {
  position: relative;
  color: transparent;
}

.AdminProfile__save-btn.loading::after {
  content: "";
  position: absolute;
  width: 20px;
  height: 20px;
  border: 2px solid #fff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 0.8s linear infinite;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

@keyframes spin {
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .AdminProfile__content {
    flex-direction: column;
    align-items: center;
    padding: 1.5rem;
  }

  .AdminProfile__form {
    flex-direction: column;
    align-items: center;
  }

  .AdminProfile__fields {
    width: 100%;
  }

  .AdminProfile__image-section {
    margin-bottom: 2rem;
  }
}

@media (max-width: 480px) {
  .AdminProfile {
    padding: 1rem;
  }

  .AdminProfile__content {
    padding: 1rem;
  }

  .AdminProfile__image-container {
    width: 150px;
    height: 150px;
  }
} 