import React from 'react';
import { FaDownload, FaExclamationTriangle } from 'react-icons/fa';
import WordDocumentViewer from './WordDocumentViewer';
import ExcelDocumentViewer from './ExcelDocumentViewer';
import '../../styles/OfficeDocumentViewer.css';

const OfficeDocumentViewer = ({
  fileUrl,
  fileName = '',
  title = 'Document',
  documentType = 'word',
  className = '',
  height = '400px',
  showDownload = false,
  onDownload = null
}) => {
  // Route to appropriate viewer based on document type
  switch (documentType) {
    case 'word':
      return (
        <WordDocumentViewer
          fileUrl={fileUrl}
          fileName={fileName}
          title={title}
          className={className}
          height={height}
          showDownload={showDownload}
          onDownload={onDownload}
        />
      );

    case 'excel':
      return (
        <ExcelDocumentViewer
          fileUrl={fileUrl}
          fileName={fileName}
          title={title}
          className={className}
          height={height}
          showDownload={showDownload}
          onDownload={onDownload}
        />
      );

    default:
      return (
        <div className={`office-document-viewer ${className}`} style={{ height }}>
          <div className="office-document-viewer__error">
            <FaExclamationTriangle />
            <h3>Unsupported Document Type</h3>
            <p>Document type "{documentType}" is not supported for preview.</p>

            {/* {showDownload && (
              <button
                className="office-document-viewer__download-button"
                onClick={onDownload || (() => window.open(fileUrl, '_blank'))}
              >
                <FaDownload />
                Download Document
              </button>
            )} */}
          </div>
        </div>
      );
  }


};

export default OfficeDocumentViewer;
