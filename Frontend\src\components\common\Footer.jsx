import React from "react";
import { Link } from "react-router-dom";
import "../../styles/Footer.css";
import logo from "../../assets/images/XOsports-hub-logo.png";
import {
  FaEnvelope,
  FaFacebookF,
} from "react-icons/fa";
import { FaSquareXTwitter } from "react-icons/fa6";
import { AiFillInstagram } from "react-icons/ai";




const Footer = () => {
  return (
    <footer className="footer-component footer">
      <div className="footer-container max-container">
        <div className="footer-logo-section">
          <img src={logo} alt="XO Sports Hub Logo" className="footer-logo" />
          <p className="footer-tagline">
            "Elevate Your Game - A Digital Exchange of Sports Strategies"
          </p>
          <div className="footer-contact">
             <div className="Contact__social-icons">
              <div className="footericonborder"><FaFacebookF  className="footer__social-icon" /></div>
              <div className="footericonborder"> <AiFillInstagram className="footer__social-icon" /></div>
              <div className="footericonborder"> <FaSquareXTwitter className="footer__social-icon" /></div>
            </div>
            <h3 className="mt-30">Get in Touch</h3>
            <p>
              <FaEnvelope size={18} /> <EMAIL>
            </p>
           
          </div>
        </div>

        <div className="footer-links">
          <h3>Quick Links</h3>
          <ul className="quickLinks">
            <li>
              <Link to="/">Home</Link>
            </li>
            <li>
              <Link to="/faq">FAQ</Link>
            </li>
            <li>
              <Link to="/contact">Contact Us</Link>
            </li>
            <li>
              <Link to="/become-instructor">Become Instructor</Link>
            </li>
            <li>
              <Link to="/terms">Terms & Rules</Link>
            </li>
            <li>
              <Link to="/privacy">Privacy Policy</Link>
            </li>
          </ul>
        </div>

        <div className="footer-sports">
          <h3>What Sport Do You Want To Learn?</h3>
          <div className="footer-sports-grid">
            <div className="footer-sports-column">
              <ul>

                <li>
                  <Link to="/">Baseball</Link>
                </li>
                <li>
                  <Link to="/">Basketball</Link>
                </li>
                <li>
                  <Link to="/">Football</Link>
                </li>
              </ul>
            </div>
            <div className="footer-sports-column">
              <ul>
                <li>
                  <Link to="/">Health & Fitness</Link>
                </li>
                <li>
                  <Link to="/">Hockey</Link>
                </li>
                <li>
                  <Link to="/">Mental Training</Link>
                </li>
              </ul>
            </div>
            <div className="footer-sports-column">
              <ul>
                <li>
                  <Link to="/">Soccer</Link>
                </li>
                <li>
                  <Link to="/">Youth coaching</Link>
                </li>
                <li>
                  <Link to="/">All sports</Link>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <div className="footer-bottom max-container">
        <p>
          &copy; {new Date().getFullYear()} Sports Playbook Strategy
          Marketplace. All rights reserved.
        </p>
      </div>
    </footer>
  );
};

export default Footer;
