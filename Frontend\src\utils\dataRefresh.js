import { 
  fetchBuyerDashboardStats,
  fetchBuyerDownloads,
  fetchBuyerRequests,
  fetchBuyerBids,
  fetchAllStrategies,
  fetchBuyerNotifications
} from '../redux/slices/buyerDashboardSlice';

/**
 * Utility functions for refreshing buyer dashboard data
 */

/**
 * Refresh all buyer dashboard data
 * @param {Function} dispatch - Redux dispatch function
 * @param {Object} options - Refresh options
 */
export const refreshAllBuyerData = async (dispatch, options = {}) => {
  const { 
    includeStats = true,
    includeDownloads = true,
    includeRequests = true,
    includeBids = true,
    includeStrategies = true,
    includeNotifications = true,
    strategiesParams = {}
  } = options;

  const promises = [];

  if (includeStats) {
    promises.push(dispatch(fetchBuyerDashboardStats()));
  }

  if (includeDownloads) {
    promises.push(dispatch(fetchBuyerDownloads()));
  }

  if (includeRequests) {
    promises.push(dispatch(fetchBuyerRequests()));
  }

  if (includeBids) {
    promises.push(dispatch(fetchBuyerBids()));
  }

  if (includeStrategies) {
    promises.push(dispatch(fetchAllStrategies(strategiesParams)));
  }

  if (includeNotifications) {
    promises.push(dispatch(fetchBuyerNotifications()));
  }

  try {
    await Promise.allSettled(promises);
    console.log('Buyer dashboard data refreshed successfully');
  } catch (error) {
    console.error('Error refreshing buyer dashboard data:', error);
  }
};

/**
 * Refresh specific section data
 * @param {Function} dispatch - Redux dispatch function
 * @param {string} section - Section to refresh
 * @param {Object} params - Additional parameters
 */
export const refreshSectionData = async (dispatch, section, params = {}) => {
  try {
    switch (section) {
      case 'stats':
        await dispatch(fetchBuyerDashboardStats());
        break;
      case 'downloads':
        await dispatch(fetchBuyerDownloads(params));
        break;
      case 'requests':
        await dispatch(fetchBuyerRequests(params));
        break;
      case 'bids':
        await dispatch(fetchBuyerBids(params));
        break;
      case 'strategies':
        await dispatch(fetchAllStrategies(params));
        break;
      case 'notifications':
        await dispatch(fetchBuyerNotifications());
        break;
      default:
        console.warn(`Unknown section: ${section}`);
    }
  } catch (error) {
    console.error(`Error refreshing ${section} data:`, error);
  }
};

/**
 * Set up automatic data refresh intervals
 * @param {Function} dispatch - Redux dispatch function
 * @param {Object} options - Interval options
 * @returns {Object} - Cleanup functions
 */
export const setupDataRefreshIntervals = (dispatch, options = {}) => {
  const {
    statsInterval = 5 * 60 * 1000, // 5 minutes
    notificationsInterval = 30 * 1000, // 30 seconds
    generalInterval = 2 * 60 * 1000, // 2 minutes
  } = options;

  const intervals = {};

  // Stats refresh interval
  if (statsInterval > 0) {
    intervals.stats = setInterval(() => {
      dispatch(fetchBuyerDashboardStats());
    }, statsInterval);
  }

  // Notifications refresh interval
  if (notificationsInterval > 0) {
    intervals.notifications = setInterval(() => {
      dispatch(fetchBuyerNotifications());
    }, notificationsInterval);
  }

  // General data refresh interval
  if (generalInterval > 0) {
    intervals.general = setInterval(() => {
      refreshAllBuyerData(dispatch, {
        includeStats: false, // Stats have their own interval
        includeNotifications: false, // Notifications have their own interval
        includeStrategies: false, // Don't auto-refresh strategies as they might have filters
      });
    }, generalInterval);
  }

  // Return cleanup function
  return {
    cleanup: () => {
      Object.values(intervals).forEach(interval => {
        if (interval) clearInterval(interval);
      });
    },
    intervals
  };
};

/**
 * Check if data needs refresh based on last update time
 * @param {string} lastUpdate - Last update timestamp
 * @param {number} maxAge - Maximum age in milliseconds
 * @returns {boolean} - Whether data needs refresh
 */
export const shouldRefreshData = (lastUpdate, maxAge = 5 * 60 * 1000) => {
  if (!lastUpdate) return true;
  
  const now = new Date().getTime();
  const updateTime = new Date(lastUpdate).getTime();
  
  return (now - updateTime) > maxAge;
};

/**
 * Debounced refresh function to prevent excessive API calls
 * @param {Function} refreshFunction - Function to call for refresh
 * @param {number} delay - Debounce delay in milliseconds
 * @returns {Function} - Debounced function
 */
export const createDebouncedRefresh = (refreshFunction, delay = 1000) => {
  let timeoutId;
  
  return (...args) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      refreshFunction(...args);
    }, delay);
  };
};

/**
 * Refresh data when user becomes active (focus/visibility change)
 * @param {Function} dispatch - Redux dispatch function
 * @param {Object} options - Options for refresh
 */
export const setupVisibilityRefresh = (dispatch, options = {}) => {
  const handleVisibilityChange = () => {
    if (!document.hidden) {
      // User is back, refresh critical data
      refreshAllBuyerData(dispatch, {
        includeStats: true,
        includeNotifications: true,
        includeDownloads: false,
        includeRequests: false,
        includeBids: false,
        includeStrategies: false,
        ...options
      });
    }
  };

  const handleFocus = () => {
    // Refresh notifications when window gains focus
    dispatch(fetchBuyerNotifications());
  };

  document.addEventListener('visibilitychange', handleVisibilityChange);
  window.addEventListener('focus', handleFocus);

  // Return cleanup function
  return () => {
    document.removeEventListener('visibilitychange', handleVisibilityChange);
    window.removeEventListener('focus', handleFocus);
  };
};

export default {
  refreshAllBuyerData,
  refreshSectionData,
  setupDataRefreshIntervals,
  shouldRefreshData,
  createDebouncedRefresh,
  setupVisibilityRefresh,
};
