const express = require('express');
const path = require('path');
const fs = require('fs');
const { convertDocument, isSupportedDocumentType, getDocumentType } = require('../utils/documentConverter');
const { protect } = require('../middleware/auth');
const { getFileUrl } = require('../utils/fileUpload');
const router = express.Router();

/**
 * @route   GET /api/document-preview/:filename
 * @desc    Get document preview (converted to HTML/JSON)
 * @access  Private
 */
router.get('/:filename', protect, async (req, res) => {
  try {
    const { filename } = req.params;
    const fileExtension = path.extname(filename);

    console.log(`[DocumentPreview] Preview request for: ${filename}`);

    // Check if document type is supported
    if (!isSupportedDocumentType(fileExtension)) {
      return res.status(400).json({
        success: false,
        message: `Document type ${fileExtension} is not supported for preview`,
        supportedTypes: ['.docx', '.xlsx', '.doc', '.xls']
      });
    }

    // Construct file path
    // Try different possible locations
    let filePath = null;
    const possiblePaths = [
      path.join('./uploads/', filename),
      path.join('./uploads/content/', filename),
      path.join('./uploads/file/', filename)
    ];

    for (const possiblePath of possiblePaths) {
      if (fs.existsSync(possiblePath)) {
        filePath = possiblePath;
        break;
      }
    }

    if (!filePath) {
      console.error(`[DocumentPreview] File not found: ${filename}`);
      return res.status(404).json({
        success: false,
        message: 'Document file not found',
        filename,
        searchedPaths: possiblePaths
      });
    }

    console.log(`[DocumentPreview] Found file at: ${filePath}`);

    // Convert document
    const conversionResult = await convertDocument(filePath, fileExtension);

    if (!conversionResult.success) {
      console.error(`[DocumentPreview] Conversion failed:`, conversionResult.error);
      return res.status(500).json({
        success: false,
        message: 'Failed to convert document for preview',
        error: conversionResult.error,
        filename,
        fileExtension
      });
    }

    // Add document type to response
    const documentType = getDocumentType(fileExtension);

    console.log(`[DocumentPreview] Conversion successful for ${documentType} document`);

    res.json({
      success: true,
      documentType,
      filename,
      fileExtension,
      data: conversionResult,
      generatedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('[DocumentPreview] Preview generation error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error during document preview generation',
      error: error.message
    });
  }
});

/**
 * @route   POST /api/document-preview/convert
 * @desc    Convert uploaded document for preview
 * @access  Public (document preview should be accessible to all users viewing content)
 */
router.post('/convert', async (req, res) => {
  try {
    const { fileUrl, fileName } = req.body;

    if (!fileUrl || !fileName) {
      return res.status(400).json({
        success: false,
        message: 'fileUrl and fileName are required'
      });
    }

    console.log(`[DocumentPreview] Convert request for: ${fileName}`);

    const fileExtension = path.extname(fileName);

    // Check if document type is supported
    if (!isSupportedDocumentType(fileExtension)) {
      return res.status(400).json({
        success: false,
        message: `Document type ${fileExtension} is not supported for preview`,
        supportedTypes: ['.docx', '.xlsx', '.doc', '.xls']
      });
    }

    // Extract filename from fileUrl if it's a path
    let actualFileName = fileName;
    if (fileUrl.includes('/')) {
      actualFileName = fileUrl.split('/').pop();
    }

    // Construct file path
    let filePath = null;
    const possiblePaths = [
      path.join('./uploads/', actualFileName),
      path.join('./uploads/content/', actualFileName),
      path.join('./uploads/file/', actualFileName),
      fileUrl.startsWith('./') ? fileUrl : null,
      fileUrl.startsWith('/uploads/') ? `.${fileUrl}` : null
    ].filter(Boolean);

    for (const possiblePath of possiblePaths) {
      if (fs.existsSync(possiblePath)) {
        filePath = possiblePath;
        break;
      }
    }

    if (!filePath) {
      console.error(`[DocumentPreview] File not found for conversion: ${actualFileName}`);
      return res.status(404).json({
        success: false,
        message: 'Document file not found for conversion',
        fileName: actualFileName,
        fileUrl,
        searchedPaths: possiblePaths
      });
    }

    console.log(`[DocumentPreview] Found file for conversion at: ${filePath}`);

    // Convert document
    const conversionResult = await convertDocument(filePath, fileExtension);

    if (!conversionResult.success) {
      console.error(`[DocumentPreview] Conversion failed:`, conversionResult.error);
      return res.status(500).json({
        success: false,
        message: 'Failed to convert document for preview',
        error: conversionResult.error,
        fileName,
        fileExtension
      });
    }

    // Add document type to response
    const documentType = getDocumentType(fileExtension);

    console.log(`[DocumentPreview] Conversion successful for ${documentType} document`);

    res.json({
      success: true,
      documentType,
      fileName,
      fileExtension,
      fileUrl,
      data: conversionResult,
      generatedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('[DocumentPreview] Conversion error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error during document conversion',
      error: error.message
    });
  }
});

/**
 * @route   GET /api/document-preview/supported-types
 * @desc    Get list of supported document types
 * @access  Public
 */
router.get('/info/supported-types', (req, res) => {
  res.json({
    success: true,
    supportedTypes: {
      word: ['.doc', '.docx', '.docm', '.dot', '.dotx', '.dotm'],
      excel: ['.xls', '.xlsx', '.xlsm', '.xlt', '.xltx', '.xltm', '.csv']
    },
    features: {
      word: 'Full HTML conversion with text, formatting, and basic images',
      excel: 'Spreadsheet data extraction with multiple sheets support'
    }
  });
});

module.exports = router;
