const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * Enhanced Document System Setup Script
 * Installs dependencies and validates the document preview system
 */

console.log('🚀 Setting up Enhanced Document System...\n');

// Check if required directories exist
const ensureDirectories = () => {
  console.log('📁 Ensuring required directories exist...');

  const directories = [
    './uploads',
    './uploads/previews',
    './temp'
  ];

  directories.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`✅ Created directory: ${dir}`);
    } else {
      console.log(`✅ Directory exists: ${dir}`);
    }
  });
  console.log('');
};

// Install npm dependencies
const installDependencies = () => {
  return new Promise((resolve, reject) => {
    console.log('📦 Installing new dependencies...');
    console.log('Installing: adm-zip, xmldom');

    exec('npm install adm-zip@^0.5.10 xmldom@^0.6.0', (error, stdout, stderr) => {
      if (error) {
        console.error('❌ Failed to install dependencies:', error);
        reject(error);
        return;
      }

      console.log('✅ Dependencies installed successfully');
      if (stdout) console.log(stdout);
      if (stderr) console.log('Warnings:', stderr);
      console.log('');
      resolve();
    });
  });
};

// Test document converter
const testDocumentConverter = async () => {
  console.log('🧪 Testing Enhanced Document Converter...');

  try {
    const {
      isSupportedDocumentType,
      getDocumentType,
      convertWordToHtml,
      convertExcelToData,
      convertPowerPointToData
    } = require('../utils/documentConverter');

    // Test supported file types
    console.log('Testing supported file types:');
    const testExtensions = ['.docx', '.xlsx', '.pptx', '.pdf', '.txt'];
    testExtensions.forEach(ext => {
      const isSupported = isSupportedDocumentType(ext);
      const type = getDocumentType(ext);
      console.log(`  ${ext}: ${isSupported ? '✅' : '❌'} supported, type: ${type}`);
    });

    console.log('✅ Document converter tests passed\n');
  } catch (error) {
    console.error('❌ Document converter test failed:', error.message);
    throw error;
  }
};

// Test preview generator
const testPreviewGenerator = async () => {
  console.log('🖼️  Testing Preview Generator...');

  try {
    const { canGeneratePreview, ensurePreviewDirectories } = require('../utils/previewGenerator');

    // Ensure preview directories
    ensurePreviewDirectories();
    console.log('✅ Preview directories created');

    // Test preview capability detection
    console.log('Testing preview capability:');
    const testFiles = [
      { type: 'document', name: 'test.docx' },
      { type: 'document', name: 'test.xlsx' },
      { type: 'document', name: 'test.pptx' },
      { type: 'pdf', name: 'test.pdf' },
      { type: 'video', name: 'test.mp4' }
    ];

    testFiles.forEach(file => {
      const canPreview = canGeneratePreview(file.type, file.name);
      console.log(`  ${file.name} (${file.type}): ${canPreview ? '✅' : '❌'} can generate preview`);
    });

    console.log('✅ Preview generator tests passed\n');
  } catch (error) {
    console.error('❌ Preview generator test failed:', error.message);
    throw error;
  }
};

// Validate file upload configuration
const validateFileUpload = () => {
  console.log('📤 Validating File Upload Configuration...');

  try {
    const { FILE_TYPE_MIMES } = require('../../Frontend/src/utils/fileValidation');

    console.log('Supported document MIME types:');
    if (FILE_TYPE_MIMES.Document) {
      console.log(`  Document types: ${FILE_TYPE_MIMES.Document.length} MIME types supported`);
    }

    console.log('✅ File upload configuration valid\n');
  } catch (error) {
    console.error('❌ File upload validation failed:', error.message);
  }
};

// Create sample preview
const createSamplePreview = async () => {
  console.log('📄 Creating sample preview data...');

  try {
    const samplePreview = {
      success: true,
      documentType: 'excel',
      metadata: {
        fileName: 'sample-spreadsheet.xlsx',
        fileType: 'Excel Spreadsheet',
        sheetCount: 3,
        fileSize: 1024000,
        lastModified: new Date().toISOString(),
        isPreview: true
      },
      preview: {
        type: 'spreadsheet',
        sheets: [
          {
            sheetName: 'Overview',
            data: [
              ['Item', 'Quantity', 'Price'],
              ['Product A', '100', '$50.00'],
              ['Product B', '75', '$25.00']
            ]
          }
        ],
        totalSheets: 3
      },
      generatedAt: new Date().toISOString()
    };

    const previewPath = './uploads/previews/sample-preview.json';
    fs.writeFileSync(previewPath, JSON.stringify(samplePreview, null, 2));
    console.log(`✅ Sample preview created: ${previewPath}\n`);
  } catch (error) {
    console.error('❌ Sample preview creation failed:', error.message);
  }
};

// Run setup
const runSetup = async () => {
  try {
    ensureDirectories();
    await installDependencies();
    await testDocumentConverter();
    await testPreviewGenerator();
    validateFileUpload();
    await createSamplePreview();

    console.log('🎉 Enhanced Document System setup completed successfully!');
    console.log('\n📋 Summary of improvements:');
    console.log('✅ Enhanced PowerPoint content extraction');
    console.log('✅ Better mobile document preview support');
    console.log('✅ Improved error handling and loading states');
    console.log('✅ Cross-platform document viewer components');
    console.log('✅ Robust document type detection');
    console.log('✅ Native mobile app integration options');
    console.log('\n🚀 Your document system is ready to handle any supported file type!');

  } catch (error) {
    console.error('\n❌ Setup failed:', error.message);
    console.log('\n🔧 Please check the error above and run the setup again.');
    process.exit(1);
  }
};

// Run if called directly
if (require.main === module) {
  runSetup();
}

module.exports = { runSetup }; 