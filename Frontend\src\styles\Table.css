/* Base table styles */

.table-container {
  width: 100%;
  border-radius: var(--border-radius-medium);
  border: 1px solid var(--light-gray);
  overflow-x: auto;
  background-color: var(--white);
}

/* Traditional table layout */
.table-container table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--smallfont);
  overflow-x: auto;
  margin: 0;
}

.table-container th,
.table-container td {
  padding: var(--smallfont) var(--extrasmallfont) !important;
  text-align: left;
  border-bottom: 1px solid var(--light-gray);
  vertical-align: middle;
  white-space: nowrap !important;
}

.table-container th {
  background-color: var(--bg-gray) !important;
  font-weight: 600 !important;
  color: var(--secondary-color) !important;
  font-size: var(--smallfont) !important;
  white-space: nowrap !important;
  letter-spacing: 0.5px;
}

.table-container tbody tr:last-child td {
  border-bottom: none;
}

/* Grid-based table layout */
.table-container.grid .table {
  display: flex;
  flex-direction: column;
  overflow: auto;
}

.table-container.grid .table-header {
  display: grid;
  background-color: var(--bg-gray);
  padding: var(--smallfont) var(--basefont);
  font-weight: 600;
  color: var(--secondary-color);
  border-bottom: 1px solid var(--light-gray);

  grid-template-columns: 0.5fr 1fr 3fr 1.5fr 1fr 1fr 0.5fr;
  gap: 10px;
  align-items: center;
}

.table-container.grid .table-row {
  display: grid;
  padding: var(--smallfont) var(--basefont);
  border-bottom: 1px solid var(--light-gray);
  align-items: center;
  /* Default grid template - will be overridden by inline styles */
  grid-template-columns: 0.5fr 1fr 3fr 1.5fr 1fr 1fr 0.5fr;
  gap: 10px;
}

.table-container.grid .table-row:last-child {
  border-bottom: none;
}

.table-container.grid .table-cell {
  font-size: var(--smallfont);
  white-space: nowrap;
  display: flex;
  justify-content: flex-start;
  gap: 10px;
  background-color: transparent;
  border-radius: none;
  box-shadow: none;
}

/* Content item styles for video/document cells */
.table-container.grid .content-item {
  display: flex;
  align-items: center;
  gap: var(--extrasmallfont);
  min-width: 0; /* Allow flex items to shrink */
}

.table-container.grid .content-image {
  flex-shrink: 0;
  width: 40px;
  height: 30px;
  border-radius: 4px;
  overflow: hidden;
}

.table-container.grid .content-image img {
  width: 100%;
  height: 100%;
  object-fit: fill;
}

.table-container.grid .content-info {
  flex: 1;
  min-width: 0;
  max-width: 200px; /* Allow text to truncate */
}

.table-container.grid .content-title {
  font-weight: 500;
  color: var(--text-color);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 2px;
}

.table-container.grid .content-coach {
  font-size: calc(var(--extrasmallfont) * 0.9);
  color: var(--dark-gray);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Common styles */
.table-container .table-row.empty-row {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f9f9f9;
}
.table-container .table-cell.full-span {
  width: 100%;
  text-align: center;
}
.table-container .empty-message {
  text-align: center !important;

  color: var(--dark-gray);
  font-style: italic;
}

.table-container .clickable {
  cursor: pointer;
}

/* Responsive styles */
@media (max-width: 1250px) {
  .table-container {
    overflow-x: auto;
    scroll-behavior: smooth;
    box-shadow: inset -10px 0 10px -10px rgba(0, 0, 0, 0.1);
  }

  .table-container table {
    min-width: 1000px;
    overflow: auto;
  }
  .table-container.grid .table-header,
  .table-container.grid .table-row {
    grid-template-columns: 0.5fr 1fr 2.5fr 1.5fr 1fr 1fr 0.5fr;
    min-width: 1000px;
  }
}

@media (max-width: 1024px) {
  .table-container table {
    min-width: 800px;
  }
  .table-container th,
  .table-container td,
  .table-container.grid .table-cell {
    font-size: var(--extrasmallfont);
  }

  /* Further adjust grid for tablets */
  .table-container.grid .table-header,
  .table-container.grid .table-row {
    /* Compress columns more for tablet view */
    grid-template-columns: 0.4fr 0.8fr 2fr 1.2fr 0.8fr 0.8fr 0.6fr;
    min-width: 800px;
  }

  .table-container.grid .table-cell {
    font-size: var(--extrasmallfont);
  }
}

/* Tablet and small desktop responsive styles */
@media (max-width: 768px) {
  .table-container {
    overflow-x: auto;
    /* Smooth scrolling on iOS */
    scroll-behavior: smooth;
    /* Add subtle shadow to indicate scrollable content */
    box-shadow: inset -10px 0 10px -10px rgba(0, 0, 0, 0.1);
  }

  /* Traditional table layout */
  .table-container table {
    min-width: 700px; /* Increased from 600px for better content visibility */
  }

  /* Grid-based table layout - maintain alignment with fixed width */
  .table-container.grid .table-header,
  .table-container.grid .table-row {
    min-width: 700px; /* Fixed minimum width to maintain alignment */
    /* Use fixed pixel values for better alignment on small screens */
    /* grid-template-columns: 40px 80px 200px 120px 80px 80px 75px !important; */
  }

  /* Adjust cell content for smaller screens */
  .table-container.grid .table-cell {
    font-size: var(--extrasmallfont);
    /* white-space: nowrap;
    overflow: hidden; */
  }

  /* Responsive content items for tablet */
  .table-container.grid .content-image {
    width: 35px;
    height: 25px;
  }

  .table-container.grid .content-title {
    font-size: var(--extrasmallfont);
    max-width: 150px;
  }

  .table-container.grid .content-coach {
    font-size: calc(var(--extrasmallfont) * 0.85);
    max-width: 120px;
  }

  /* Make headers sticky during horizontal scroll */
  .table-container th {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: var(--bg-gray);
  }

  .table-container.grid .table-header {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: var(--bg-gray);
  }
}

/* Mobile responsive styles */
@media (max-width: 480px) {
  .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
    /* Enhanced shadow for mobile to better indicate scrollable content */
    box-shadow: inset -15px 0 15px -15px rgba(0, 0, 0, 0.15);
  }

  /* Traditional table layout - mobile optimized */
  .table-container table {
    min-width: 800px; /* Increased for mobile to ensure all content is visible */
  }

  .table-container th,
  .table-container td {
    padding: var(--extrasmallfont) calc(var(--extrasmallfont) / 2);
    font-size: calc(var(--extrasmallfont) * 0.9);
    white-space: nowrap; /* Prevent text wrapping in cells */
  }

  /* Grid-based table layout - mobile optimized with fixed pixel columns */
  .table-container.grid .table-header,
  .table-container.grid .table-row {
    min-width: 800px; /* Increased for mobile */
    /* Use smaller fixed pixel values for mobile */
    /* grid-template-columns: 35px 70px 180px 100px 70px 70px 75px !important; */
  }

  .table-container.grid .table-cell {
    font-size: calc(var(--extrasmallfont) * 0.85);
    /* white-space: nowrap;
    overflow: hidden; */
  }

  /* Responsive content items for mobile */
  .table-container.grid .content-image {
    width: 30px;
    height: 22px;
  }

  .table-container.grid .content-title {
    font-size: calc(var(--extrasmallfont) * 0.9);
    max-width: 120px;
  }

  .table-container.grid .content-coach {
    font-size: calc(var(--extrasmallfont) * 0.8);
    max-width: 100px;
  }

  /* Status badges and action buttons for mobile */
  .table-container.grid .status-badge {
    font-size: calc(var(--extrasmallfont) * 0.8);
    padding: 2px 6px;
    border-radius: var(--border-radius-medium);
    white-space: nowrap;
  }

  .table-container.grid .action-btn {
    padding: 4px;
    font-size: calc(var(--extrasmallfont) * 0.9);
  }

  /* Enhanced sticky headers for mobile */
  .table-container th {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: var(--bg-gray);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .table-container.grid .table-header {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: var(--bg-gray);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

/* Extra small mobile devices */
@media (max-width: 320px) {
  .table-container table {
    min-width: 900px; /* Even larger minimum width for very small screens */
  }

  .table-container.grid .table-header,
  .table-container.grid .table-row {
    min-width: 900px;
    /* Even smaller fixed pixel values for very small screens */
    /* grid-template-columns: 30px 60px 160px 90px 60px 60px 75px !important; */
  }

  .table-container th {
    font-size: calc(var(--extrasmallfont) * 0.8);
    padding: 10px;
  }
  .table-container td,
  .table-container.grid .table-cell {
    font-size: calc(var(--extrasmallfont) * 0.8);
  }
}

/* Show scroll indicators only on small screens */
@media (max-width: 768px) {
  .table-container {
    position: relative;
  }
}

/* Custom scrollbar styling for webkit browsers */
.table-container::-webkit-scrollbar {
  height: 4px;
}

.table-container::-webkit-scrollbar-track {
  background: var(--light-gray);
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb {
  background: var(--dark-gray) !important;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-color);
}

/* Custom variants can be added here when needed */
