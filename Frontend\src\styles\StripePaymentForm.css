/* Scoped Stripe Payment Form Styles - Prevents conflicts with other app styles */
.stripe-payment-container .stripe-payment-form {
  max-width: 100%;
  margin: 0;
  padding: 0;
  background: transparent;
  border-radius: 0;
  box-shadow: none;
}

.stripe-payment-container .payment-header {
  text-align: left;
  margin-bottom: var(--heading5);
  padding-bottom: var(--heading6);
  border-bottom: 1px solid var(--light-gray);
}

.stripe-payment-container .payment-header h3 {
  font-size: var(--heading5);
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: var(--smallfont);
}

.stripe-payment-container .payment-header p {
  color: var(--dark-gray);
  font-size: var(--smallfont);
  margin: 0;
}

.stripe-payment-container .payment-form {
  display: flex;
  flex-direction: column;
  gap: var(--heading5);
}

.stripe-payment-container .billing-details {
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
}

.stripe-payment-container .form-group {
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
}

.stripe-payment-container .form-group label {
  font-weight: 600;
  color: var(--secondary-color);
  font-size: var(--smallfont);
  margin-bottom: 6px;
}

.stripe-payment-container .form-input {
  padding: var(--basefont) var(--heading6);
  border: 2px solid var(--light-gray);
  border-radius: var(--border-radius-medium);
  font-size: var(--basefont);
  color: var(--text-color);
  background-color: var(--white);
  transition: all 0.3s ease;
  font-family: inherit;
  min-height: 50px;
  position: relative;
}

.stripe-payment-container .form-input:focus {
  outline: none;
  border-color: var(--btn-color);
  box-shadow: 0 0 0 3px rgba(238, 52, 37, 0.1);
  transform: translateY(-1px);
}

.stripe-payment-container .form-input:hover {
  border-color: var(--btn-color);
  box-shadow: 0 2px 8px rgba(238, 52, 37, 0.1);
}

.stripe-payment-container .card-element-container {
  padding: var(--basefont) var(--heading6);
  border: 2px solid var(--light-gray);
  border-radius: var(--border-radius-medium);
  background: var(--white);
  transition: all 0.3s ease;
  min-height: 50px;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.stripe-payment-container .card-element-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: transparent;
  transition: background 0.3s ease;
}

.stripe-payment-container .card-element-container:focus-within {
  border-color: var(--btn-color);
  box-shadow: 0 0 0 3px rgba(238, 52, 37, 0.1);
  transform: translateY(-1px);
}

.stripe-payment-container .card-element-container:focus-within::before {
  background: var(--btn-color);
}

.stripe-payment-container .card-element-container:hover {
  border-color: var(--btn-color);
  box-shadow: 0 2px 8px rgba(238, 52, 37, 0.1);
}

.stripe-payment-container .card-element-loading {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  color: var(--dark-gray);
  font-size: var(--smallfont);
  padding: var(--smallfont) 0;
}

.stripe-payment-container .card-element-loading .spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--light-gray);
  border-top: 2px solid var(--btn-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.stripe-payment-container .card-element-placeholder {
  display: flex;
  align-items: center;
  color: var(--dark-gray);
  font-size: var(--smallfont);
  padding: var(--smallfont) 0;
  opacity: 0.7;
}

.stripe-payment-container .stripe-card-element {
  width: 100%;
  min-height: 20px;
}

.stripe-payment-container .stripe-card-element-wrapper {
  width: 100%;
  min-height: 40px;
  display: flex;
  align-items: center;
}

.stripe-payment-container .card-error {
  color: var(--error-color);
  font-size: var(--smallfont);
  margin-top: 6px;
  padding: var(--smallfont);
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  gap: 8px;
}

.stripe-payment-container .card-error::before {
  content: "⚠️";
  font-size: var(--basefont);
}

.stripe-payment-container .payment-error {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: var(--error-color);
  padding: var(--basefont);
  border-radius: var(--border-radius-medium);
  text-align: center;
}

.stripe-payment-container .order-summary-payment {
  background: var(--bg-gray);
  padding: var(--heading6);
  border-radius: var(--border-radius-medium);
  border: 1px solid var(--light-gray);
  margin-top: var(--basefont);
}

.stripe-payment-container .summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--smallfont) 0;
  font-size: var(--smallfont);
  color: var(--text-color);
}

.stripe-payment-container .summary-row.total {
  border-top: 1px solid var(--light-gray);
  margin-top: var(--smallfont);
  padding-top: var(--basefont);
  font-weight: 600;
  font-size: var(--basefont);
  color: var(--secondary-color);
}

.stripe-payment-container .payment-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--basefont);
  margin-top: var(--heading6);
  padding-top: var(--heading6);
  border-top: 1px solid var(--light-gray);
}

.stripe-payment-container .cancel-btn {
  flex: 1;
  padding: var(--smallfont) var(--heading5);
  border: 1px solid var(--light-gray);
  background: var(--white);
  color: var(--dark-gray);
  border-radius: var(--border-radius-medium);
  font-weight: 500;
  font-size: var(--basefont);
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: inherit;
}

.stripe-payment-container .cancel-btn:hover:not(:disabled) {
  background: var(--bg-gray);
  border-color: var(--dark-gray);
  transform: translateY(-1px);
  box-shadow: var(--box-shadow-light);
}

.stripe-payment-container .pay-btn {
  flex: 2;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--smallfont);
}

.stripe-payment-container .pay-btn:hover:not(:disabled) {
 transform: scale(1.02);
}

.stripe-payment-container .pay-btn:disabled {
  background: var(--dark-gray);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.stripe-payment-container .pay-btn.processing {
  position: relative;
  color: transparent;
}

.stripe-payment-container .pay-btn.processing::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid var(--white);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stripe-payment-container .payment-method-selection,
.stripe-payment-container .billing-details,
.stripe-payment-container .form-group {
  animation: fadeInUp 0.6s ease-out;
}

.stripe-payment-container .spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.stripe-payment-container .security-notice {
  text-align: center;
  margin-top: var(--heading5);
  padding: var(--basefont);
  background: linear-gradient(135deg, var(--bg-blue) 0%, var(--primary-light-color) 100%);
  border-radius: var(--border-radius-medium);
  border: 1px solid var(--light-gray);
}

.stripe-payment-container .security-notice p {
  color: var(--dark-gray);
  font-size: var(--smallfont);
  margin: 0;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--smallfont);
}

/* Responsive Design */
@media (max-width: 768px) {
  .stripe-payment-container .payment-header h3 {
    font-size: var(--heading6);
  }

  .stripe-payment-container .payment-actions {
    grid-template-columns: 1fr;
    gap: var(--smallfont);
  }

  .stripe-payment-container .cancel-btn,
  .stripe-payment-container .pay-btn {
    flex: none;
    padding: var(--basefont) var(--heading5);
  }

  .stripe-payment-container .form-input {
    font-size: var(--basefont);
  }

  .stripe-payment-container .payment-method-selection {
    padding: var(--basefont);
  }

  .stripe-payment-container .card-info {
    gap: var(--smallfont);
  }

  .stripe-payment-container .card-icon {
    min-width: 36px;
    height: 36px;
    font-size: var(--basefont);
  }
}

/* Loading state overlay */
.stripe-payment-container .payment-form.processing {
  position: relative;
  pointer-events: none;
}

.stripe-payment-container .payment-form.processing::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  z-index: 10;
}

/* Payment Method Selection Styles */
.stripe-payment-container .payment-method-selection {
  margin-bottom: var(--heading5);
  padding: var(--heading6);
  border: 2px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow-light);
  position: relative;
  overflow: hidden;
}

.stripe-payment-container .payment-method-selection h4 {
  margin: 0 0 var(--heading6) 0;
  color: var(--secondary-color);
  font-size: var(--heading6);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  padding-bottom: var(--smallfont);
  border-bottom: 1px solid var(--light-gray);
}

.stripe-payment-container .payment-option {
  margin-bottom: var(--basefont);
  padding: var(--basefont);
  border-radius: var(--border-radius-medium);
  transition: all 0.3s ease;
  border: 1px solid transparent;
  background: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  gap: 10px;
}

.stripe-payment-container .payment-option:last-child {
  margin-bottom: 0;
}

.stripe-payment-container .payment-option:hover {
  background: rgba(var(--primary-rgb), 0.05);
  border-color: var(--btn-color);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(238, 52, 37, 0.1);
}

.stripe-payment-container .payment-option input[type="radio"] {
  margin-right: var(--basefont);
  accent-color: var(--btn-color);
  width: 20px;
  height: 20px;
  cursor: pointer;
}

.stripe-payment-container .payment-option label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: 500;
  color: var(--secondary-color);
  font-size: var(--basefont);
  transition: all 0.3s ease;
  width: 100%;
}

.stripe-payment-container .payment-option input[type="radio"]:checked + label {
  color: var(--btn-color);
  font-weight: 600;
}

.stripe-payment-container .payment-option:has(input[type="radio"]:checked) {
  background: transparent;
  border-color: var(--btn-color);
  box-shadow: 0 0 0 2px rgba(238, 52, 37, 0.1);
}

.stripe-payment-container .plus-icon {
  margin-right: var(--smallfont);
  color: var(--btn-color);
  font-size: var(--basefont);
}

/* Saved Cards List */
.stripe-payment-container .saved-cards-section {
  margin-bottom: var(--basefont);
  margin-top: var(--basefont);
}

.stripe-payment-container .saved-cards-list {
  margin-top: var(--basefont);
  margin-bottom: var(--basefont);
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
  padding: var(--basefont);
  background: rgba(255, 255, 255, 0.5);
  border-radius: var(--border-radius-medium);
  border: 1px solid var(--light-gray);
}

.stripe-payment-container .saved-card-item {
  padding: var(--extrasmallfont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-medium);
  background: var(--white);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.stripe-payment-container .saved-card-item:hover {
  border-color: var(--btn-color);
  box-shadow: 0 4px 12px rgba(238, 52, 37, 0.15);
 transform: scale(1.02);
}

.stripe-payment-container .saved-card-item:has(input[type="radio"]:checked) {
  border-color: var(--btn-color);
  background: rgba(238, 52, 37, 0.02);
  box-shadow: 0 0 0 2px rgba(238, 52, 37, 0.1);
}

.stripe-payment-container .saved-card-item:has(input[type="radio"]:checked)::before {
  background: var(--btn-color);
}

.stripe-payment-container .saved-card-item input[type="radio"]:checked + .card-label {
  color: var(--btn-color);
}

.stripe-payment-container .saved-card-item input[type="radio"] {
  margin-right: var(--basefont);
  accent-color: var(--btn-color);
  width: 20px;
  height: 20px;
  cursor: pointer;
}

.stripe-payment-container .card-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  width: 100%;
  font-weight: 500;
}

.stripe-payment-container .card-info {
  display: flex;
  align-items: center;
  gap: var(--basefont);
  flex: 1;
}

.stripe-payment-container .card-icon {
  color: var(--dark-gray);
  font-size: var(--heading6);
  padding: var(--smallfont);
  background: var(--bg-gray);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 40px;
}

.stripe-payment-container .card-details {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  flex: 1;
}

.stripe-payment-container .cards-details-style {
  display: grid;
  justify-content: flex-start;
  gap: 10px;
  align-items: center;
}

.stripe-payment-container .card-number {
  font-weight: 600;
  color: var(--secondary-color);
  font-size: var(--basefont);
  letter-spacing: 0.5px;
}

.stripe-payment-container .card-type {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  text-transform: uppercase;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.stripe-payment-container .card-expiry {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

.stripe-payment-container .default-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px var(--smallfont);
  background: linear-gradient(135deg, #28a745, #20c997);
  color: var(--white);
  font-size: var(--extrasmallfont);
  border-radius: var(--border-radius);
  font-weight: 600;
  text-transform: uppercase;
  width: fit-content;
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
  letter-spacing: 0.5px;
}

.stripe-payment-container .default-badge::before {
  content: "⭐";
  margin-right: 4px;
  font-size: var(--smallfont);
}

/* Responsive Design for Payment Method Selection */
@media (max-width: 768px) {
  .stripe-payment-container .payment-method-selection {
    padding: 16px;
  }

  .stripe-payment-container .card-info {
    gap: 8px;
  }

  .stripe-payment-container .card-details {
    gap: 1px;
  }

  .stripe-payment-container .saved-card-item {
    padding: 10px;
  }
}

@media (max-width: 500px) {
  .stripe-payment-container .card-details {
    gap: 1px;
    display: grid;
  }

  .stripe-payment-container .payment-method-selection {
    padding: 0px;
    border: none;
    border-radius: 0px;
    box-shadow: none;
  }

  .stripe-payment-container .saved-cards-list {
    padding: 0px;
    border: none;
  }
}