import React, { useState, useEffect } from 'react';
import { FaDownload, FaExclamationTriangle, FaSync, FaFileWord } from 'react-icons/fa';
import { API_BASE_URL } from '../../utils/constants';
import '../../styles/WordDocumentViewer.css';

const WordDocumentViewer = ({
  fileUrl,
  fileName = '',
  title = 'Word Document',
  className = '',
  height = '400px',
  showDownload = false,
  onDownload = null
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [documentData, setDocumentData] = useState(null);
  const [errorMessage, setErrorMessage] = useState('');

  // Extract filename from fileUrl for API call
  const getFileName = () => {
    if (fileName) return fileName;
    if (fileUrl) return fileUrl.split('/').pop();
    return 'document.docx';
  };

  // Load document preview
  useEffect(() => {
    const loadDocumentPreview = async () => {
      if (!fileUrl) {
        setHasError(true);
        setErrorMessage('No file URL provided');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setHasError(false);

        const actualFileName = getFileName();
        console.log(`[WordViewer] Loading preview for: ${actualFileName}`);

        // Check if fileUrl is a JSON preview file
        if (fileUrl.endsWith('.json')) {
          console.log(`[WordViewer] Loading JSON preview from: ${fileUrl}`);

          // Fetch the JSON preview directly
          const response = await fetch(fileUrl);
          if (!response.ok) {
            throw new Error(`Failed to load preview: ${response.status}`);
          }

          const previewData = await response.json();
          if (previewData.success) {
            setDocumentData(previewData);
          } else {
            throw new Error(previewData.error || 'Invalid preview data');
          }
        } else {
          // Call our custom document preview API for live conversion
          const response = await fetch(`${API_BASE_URL}/document-preview/convert`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify({
              fileUrl: fileUrl,
              fileName: actualFileName
            })
          });

          if (!response.ok) {
            throw new Error(`Preview generation failed: ${response.status}`);
          }

          const result = await response.json();

          if (!result.success) {
            throw new Error(result.message || 'Failed to generate preview');
          }

          setDocumentData(result.data);
        }

        console.log(`[WordViewer] Preview loaded successfully`);
        setIsLoading(false);

      } catch (error) {
        console.error('[WordViewer] Preview loading failed:', error);
        setHasError(true);
        setErrorMessage(error.message);
        setIsLoading(false);
      }
    };

    loadDocumentPreview();
  }, [fileUrl, fileName]);

  // Handle download - DISABLED FOR SECURITY
  const handleDownload = () => {
    console.warn('Download functionality has been disabled for security purposes');
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className={`word-document-viewer ${className}`} style={{ height }}>
        <div className="word-document-viewer__header">
          <div className="word-document-viewer__info">
            <span className="word-document-viewer__title">{title}</span>
            <span className="word-document-viewer__type">Microsoft Word Document</span>
          </div>
          {/* {showDownload && (
            <button 
              className="word-document-viewer__download-btn"
              onClick={handleDownload}
              title="Download Document"
            >
              <FaDownload />
            </button>
          )} */}
        </div>

        <div className="word-document-viewer__loading">
          <FaSync className="spinning" />
          <p>Converting Word document...</p>
          <p className="word-document-viewer__loading-info">
            This may take a moment for large documents
          </p>
        </div>
      </div>
    );
  }

  // Render error state
  if (hasError) {
    return (
      <div className={`word-document-viewer ${className}`} style={{ height }}>
        <div className="word-document-viewer__header">
          <div className="word-document-viewer__info">
            <span className="word-document-viewer__title">{title}</span>
            <span className="word-document-viewer__type">Microsoft Word Document</span>
          </div>
          {/* {showDownload && (
            <button 
              className="word-document-viewer__download-btn"
              onClick={handleDownload}
              title="Download Document"
            >
              <FaDownload />
            </button>
          )} */}
        </div>

        <div className="word-document-viewer__error">
          <FaExclamationTriangle />
          <h3>Preview Not Available</h3>
          <p>Unable to generate preview for this Word document.</p>
          <p className="word-document-viewer__error-details">{errorMessage}</p>

          {showDownload && (
            <button
              className="word-document-viewer__download-button"
              onClick={handleDownload}
            >
              <FaDownload />
              Download Word Document
            </button>
          )}
        </div>
      </div>
    );
  }

  // Render document content
  return (
    <div className={`word-document-viewer ${className}`} style={{ height }}>

      <div className="word-document-viewer__content">
        {documentData?.html ? (
          <div
            className="word-document-viewer__html-content"
            dangerouslySetInnerHTML={{ __html: documentData.html }}
          />
        ) : (
          <div className="word-document-viewer__no-content">
            <FaFileWord />
            <p>Document content could not be displayed</p>
            <p>Please download the file to view the full content</p>
          </div>
        )}
      </div>

    </div>
  );
};

export default WordDocumentViewer;
