/**
 * File validation utilities for content type-based file uploads
 */

// Define supported file extensions for each content type
export const FILE_TYPE_EXTENSIONS = {
  Video: [
    '.mp4', '.mov', '.avi', '.webm'  // Only allowing specified video formats
  ],
  Document: [
    '.pdf', '.doc', '.docx'  // Only allowing specified document formats
  ]
};

// Define supported MIME types for each content type
export const FILE_TYPE_MIMES = {
  Video: [
    'video/mp4', 'video/quicktime', 'video/x-msvideo', 'video/webm'
  ],
  Document: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ]
};

// Define maximum file sizes (in bytes)
export const FILE_SIZE_LIMITS = {
  Video: 500 * 1024 * 1024, // 500MB
  Document: 50 * 1024 * 1024 // 50MB
};

/**
 * Get file extension from filename
 * @param {string} filename - The filename
 * @returns {string} - The file extension (lowercase with dot)
 */
export const getFileExtension = (filename) => {
  if (!filename) return '';
  const lastDotIndex = filename.lastIndexOf('.');
  if (lastDotIndex === -1) return '';
  return filename.substring(lastDotIndex).toLowerCase();
};

/**
 * Validate file based on selected content type
 * @param {File} file - The file object
 * @param {string} contentType - The selected content type ('Video' or 'Document')
 * @returns {Object} - Validation result with isValid and message
 */
export const validateFileByContentType = (file, contentType) => {
  if (!file) {
    return {
      isValid: false,
      message: 'No file selected'
    };
  }

  if (!contentType) {
    return {
      isValid: false,
      message: 'Please select a content type first'
    };
  }

  const fileExtension = getFileExtension(file.name);
  const allowedExtensions = FILE_TYPE_EXTENSIONS[contentType];
  const allowedMimes = FILE_TYPE_MIMES[contentType];
  const sizeLimit = FILE_SIZE_LIMITS[contentType];

  // Check file size
  if (file.size > sizeLimit) {
    const sizeMB = sizeLimit / (1024 * 1024);
    return {
      isValid: false,
      message: `File size exceeds the maximum limit of ${sizeMB}MB for ${contentType.toLowerCase()} files`
    };
  }
  
  if (!allowedExtensions.includes(fileExtension)) {
    const extensionList = allowedExtensions.join(', ').toUpperCase();
    return {
      isValid: false,
      message: `Invalid file format for ${contentType}. Supported formats: ${extensionList}`
    };
  }

  // Check MIME type
  if (!allowedMimes.includes(file.type)) {
    return {
      isValid: false,
      message: `Invalid file type for ${contentType}. Please select a valid ${contentType.toLowerCase()} file.`
    };
  }

  return {
    isValid: true,
    message: 'File validation successful'
  };
};

/**
 * Get accept attribute value for file input based on content type
 * @param {string} contentType - The content type ('Video' or 'Document')
 * @returns {string} - Accept attribute value
 */
export const getAcceptAttribute = (contentType) => {
  if (!contentType) return '';
  return FILE_TYPE_EXTENSIONS[contentType].join(',');
};

/**
 * Check if file upload should be disabled based on content type
 * @param {string} contentType - The content type
 * @returns {boolean} - Whether file upload should be disabled
 */
export const isFileUploadDisabled = (contentType) => {
  return !contentType;
};

/**
 * Get placeholder text for file upload based on content type
 * @param {string} contentType - The content type
 * @returns {string} - Placeholder text
 */
export const getFileUploadPlaceholder = (contentType) => {
  if (!contentType) return 'Please select a content type first';
  
  const sizeLimitMB = FILE_SIZE_LIMITS[contentType] / (1024 * 1024);
  const extensions = FILE_TYPE_EXTENSIONS[contentType].join(', ').toUpperCase();
  
  return `Upload ${contentType} (Max: ${sizeLimitMB}MB, Formats: ${extensions})`;
};

export default {
  FILE_TYPE_EXTENSIONS,
  FILE_TYPE_MIMES,
  FILE_SIZE_LIMITS,
  getFileExtension,
  validateFileByContentType,
  getAcceptAttribute,
  isFileUploadDisabled,
  getFileUploadPlaceholder
};
