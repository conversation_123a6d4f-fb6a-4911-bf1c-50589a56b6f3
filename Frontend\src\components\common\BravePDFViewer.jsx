import React, { useState } from 'react';
import { FaDownload, FaExternalLinkAlt, FaExclamationTriangle } from 'react-icons/fa';
import CustomPDFViewer from './CustomPDFViewer';
import '../../styles/BravePDFViewer.css';

const BravePDFViewer = ({
    fileUrl,
    title = 'PDF Document',
    className = '',
    height = '100%',
    showDownload = false,
    onDownload = null
}) => {
    const [viewerError, setViewerError] = useState(false);
    const [showFallback, setShowFallback] = useState(false);

    const handleOpenInNewTab = () => {
        window.open(fileUrl, '_blank', 'noopener,noreferrer');
    };

    const handleDownload = () => {
        // Download functionality disabled for security purposes
        console.warn('Download functionality has been disabled for security purposes');
    };

    if (showFallback || viewerError) {
        return (
            <div className={`brave-pdf-viewer ${className}`} style={{ height }}>
                <div className="brave-pdf-viewer__fallback">
                    <div className="brave-pdf-viewer__icon">
                        <FaExclamationTriangle />
                    </div>
                    <h3>PDF Preview Blocked</h3>
                    <p>Brave browser has blocked this PDF preview for security reasons.</p>
                    <p>You can still view or download the PDF using the options below:</p>

                    <div className="brave-pdf-viewer__actions">
                        <button
                            className="brave-pdf-viewer__btn brave-pdf-viewer__btn--primary"
                            onClick={handleOpenInNewTab}
                        >
                            <FaExternalLinkAlt /> Open in New Tab
                        </button>

                        {showDownload && (
                            <button
                                className="brave-pdf-viewer__btn brave-pdf-viewer__btn--secondary"
                                onClick={handleDownload}
                            >
                                <FaDownload /> Download PDF
                            </button>
                        )}

                        <button
                            className="brave-pdf-viewer__btn brave-pdf-viewer__btn--tertiary"
                            onClick={() => setShowFallback(false)}
                        >
                            Try PDF Viewer Again
                        </button>
                    </div>

                    <div className="brave-pdf-viewer__help">
                        <p><strong>To enable PDF previews in Brave:</strong></p>
                        <ol>
                            <li>Click the Brave Shield icon (🛡️) in the address bar</li>
                            <li>Turn off "Block Scripts" for this site</li>
                            <li>Refresh the page</li>
                        </ol>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className={`brave-pdf-viewer ${className}`} style={{ height }}>
            <CustomPDFViewer
                fileUrl={fileUrl}
                title={title}
                height="100%"
                showDownload={showDownload}
                showPrint={false}
                className="brave-pdf-viewer__custom"
                onError={() => {
                    console.log('PDF viewer failed, showing fallback options');
                    setViewerError(true);
                }}
            />

            {/* Quick fallback button */}
            <div className="brave-pdf-viewer__quick-actions">
                <button
                    className="brave-pdf-viewer__quick-btn"
                    onClick={() => setShowFallback(true)}
                    title="Having trouble viewing? Click for alternatives"
                >
                    <FaExclamationTriangle /> Issues viewing?
                </button>
            </div>
        </div>
    );
};

export default BravePDFViewer; 