/* Excel Document Viewer Styles */
.excel-document-viewer {
  display: flex;
  flex-direction: column;
  background-color: var(--white);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
  position: relative;
}

/* Header */
.excel-document-viewer__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--smallfont) var(--basefont);
  background-color: var(--light-gray);
  border-bottom: 1px solid var(--border-color);
  min-height: 60px;
}

.excel-document-viewer__info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.excel-document-viewer__title {
  font-size: var(--smallfont);
  font-weight: 600;
  color: var(--secondary-color);
  line-height: 1.2;
}

.excel-document-viewer__type {
  font-size: var(--tinyfont);
  color: #217346; /* Excel green color */
  line-height: 1.2;
  font-weight: 500;
}

.excel-document-viewer__stats {
  font-size: var(--tinyfont);
  color: var(--text-muted);
  line-height: 1.2;
}

.excel-document-viewer__download-btn {
  background: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  padding: var(--tinyfont) var(--smallfont);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--tinyfont);
  font-size: var(--smallfont);
  transition: all 0.3s ease;
  min-width: 40px;
  height: 36px;
  justify-content: center;
}

.excel-document-viewer__download-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
}

/* Sheet tabs */
.excel-document-viewer__sheet-tabs {
  display: flex;
  background-color: #f8f9fa;
  border-bottom: 1px solid var(--border-color);
  overflow-x: auto;
  padding: 0 var(--smallfont);
}

.excel-sheet-tab {
  display: flex;
  align-items: center;
  gap: var(--tinyfont);
  padding: var(--tinyfont) var(--smallfont);
  background: transparent;
  border: none;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  font-size: var(--tinyfont);
  color: var(--text-muted);
  white-space: nowrap;
  transition: all 0.2s ease;
  min-width: 80px;
  justify-content: center;
}

.excel-sheet-tab:hover {
  background-color: var(--light-gray);
  color: var(--secondary-color);
}

.excel-sheet-tab--active {
  color: #217346;
  border-bottom-color: #217346;
  background-color: var(--white);
  font-weight: 500;
}

.excel-sheet-tab svg {
  font-size: 10px;
}

/* Content area */
.excel-document-viewer__content {
  flex: 1;
  overflow: auto;
  background-color: var(--white);
}

.excel-table-container {
  width: 100%;
  overflow: auto;
}

.excel-table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--tinyfont);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.excel-table__row {
  border-bottom: 1px solid #e0e0e0;
}

.excel-table__row:nth-child(even) {
  background-color: #f9f9f9;
}

.excel-table__row:hover {
  background-color: #f0f8ff;
}

.excel-table__cell {
  padding: var(--tinyfont) var(--smallfont);
  border-right: 1px solid #e0e0e0;
  text-align: left;
  vertical-align: top;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.4;
}

.excel-table__header-cell {
  background-color: #217346;
  color: var(--white);
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 10;
}

.excel-table__cell:last-child {
  border-right: none;
}

/* Truncated notice */
.excel-document-viewer__truncated-notice {
  padding: var(--basefont);
  background-color: #fff3cd;
  border-top: 1px solid #ffeaa7;
  text-align: center;
  color: #856404;
}

.excel-document-viewer__truncated-notice p {
  margin: 0 0 var(--tinyfont) 0;
  font-size: var(--tinyfont);
}

.excel-document-viewer__truncated-notice p:last-child {
  margin-bottom: 0;
}

/* No content state */
.excel-document-viewer__no-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
  padding: var(--heading5);
  text-align: center;
  color: var(--text-muted);
}

.excel-document-viewer__no-content svg {
  font-size: var(--heading3);
  color: #217346;
  margin-bottom: var(--basefont);
}

.excel-document-viewer__no-content p {
  margin: 0 0 var(--tinyfont) 0;
  font-size: var(--smallfont);
}

/* Loading state */
.excel-document-viewer__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 300px;
  color: var(--text-muted);
  background-color: var(--light-gray);
}

.excel-document-viewer__loading .spinning {
  font-size: var(--heading5);
  margin-bottom: var(--basefont);
  animation: spin 1s linear infinite;
}

.excel-document-viewer__loading p {
  margin: 0;
  font-size: var(--smallfont);
  text-align: center;
}

.excel-document-viewer__loading-info {
  color: var(--primary-color) !important;
  font-size: var(--tinyfont) !important;
  margin-top: var(--tinyfont) !important;
}

/* Error state */
.excel-document-viewer__error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 300px;
  padding: var(--heading5);
  text-align: center;
  color: var(--text-muted);
  background-color: var(--light-gray);
}

.excel-document-viewer__error svg {
  font-size: var(--heading3);
  color: var(--warning-color);
  margin-bottom: var(--basefont);
}

.excel-document-viewer__error h3 {
  margin: 0 0 var(--smallfont) 0;
  font-size: var(--heading6);
  color: var(--secondary-color);
}

.excel-document-viewer__error p {
  margin: 0 0 var(--tinyfont) 0;
  font-size: var(--smallfont);
  line-height: 1.4;
}

.excel-document-viewer__error-details {
  color: var(--error-color) !important;
  font-size: var(--tinyfont) !important;
  font-family: monospace !important;
}

.excel-document-viewer__download-button {
  background: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  padding: var(--smallfont) var(--basefont);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--tinyfont);
  font-size: var(--smallfont);
  margin-top: var(--basefont);
  transition: all 0.3s ease;
}

.excel-document-viewer__download-button:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
}

/* Footer */
.excel-document-viewer__footer {
  border-top: 1px solid var(--border-color);
  padding: var(--tinyfont) var(--basefont);
  background-color: var(--light-gray);
}

.excel-document-viewer__metadata {
  display: flex;
  gap: var(--basefont);
  flex-wrap: wrap;
}

.excel-document-viewer__meta-item {
  font-size: var(--tinyfont);
  color: var(--text-muted);
  padding: 2px var(--tinyfont);
  background-color: var(--white);
  border-radius: 3px;
  border: 1px solid var(--border-color);
}

/* Responsive design */
@media (max-width: 768px) {
  .excel-document-viewer__header {
    padding: var(--tinyfont) var(--smallfont);
    min-height: 50px;
  }
  
  .excel-document-viewer__title {
    font-size: var(--tinyfont);
  }
  
  .excel-document-viewer__type,
  .excel-document-viewer__stats {
    font-size: 10px;
  }
  
  .excel-document-viewer__download-btn {
    min-width: 35px;
    height: 32px;
    padding: var(--tinyfont);
  }
  
  .excel-sheet-tab {
    min-width: 60px;
    padding: var(--tinyfont);
    font-size: 10px;
  }
  
  .excel-table__cell {
    padding: 2px var(--tinyfont);
    max-width: 100px;
    font-size: 10px;
  }
  
  .excel-document-viewer__loading,
  .excel-document-viewer__error {
    min-height: 250px;
    padding: var(--basefont);
  }
}

/* Animation for spinning icon */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
