import React from "react";
import "../../styles/Table.css";

/**
 * Reusable Table Component
 * A flexible table component that maintains existing UI while reducing code duplication
 *
 * @param {Object} props
 * @param {Array} props.columns - Array of column configurations
 * @param {Array} props.data - Array of data to display
 * @param {Function} props.onRowClick - Optional click handler for rows
 * @param {Function} props.renderRow - Optional custom row renderer
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.emptyMessage - Message to show when no data
 * @param {string} props.variant - Table style variant (default, grid, or custom)
 * @param {string} props.gridTemplate - Grid template columns for responsive layout
 */
const Table = ({
  columns,
  data,
  onRowClick,
  renderRow,
  className = "",
  emptyMessage = "No data available",
  variant = "default",
  gridTemplate = "",
}) => {
  const handleRowClick = (item) => {
    if (onRowClick) {
      onRowClick(item);
    }
  };

  return (
    <div className={`table-container ${variant} ${className}`}>
      {variant === "grid" ? (
        // Grid-based table layout (used in BuyerAccountDashboard)
        <div className={`table ${className}`}>
          <div
            className="table-header"
            style={gridTemplate ? { gridTemplateColumns: gridTemplate } : {}}
          >
            {columns.map((column) => (
              <div
                key={column.key}
                className={`table-cell ${column.className || ""}`}
              >
                {column.label}
              </div>
            ))}
          </div>
          {data.length > 0 ? (
            data.map((item, index) => (
              <div
                key={item.id || index}
                className="table-row"
                style={
                  gridTemplate ? { gridTemplateColumns: gridTemplate } : {}
                }
                onClick={() => handleRowClick(item)}
              >
                {renderRow
                  ? renderRow(item, index)
                  : columns.map((column) => (
                      <div
                        key={column.key}
                        className={`table-cell ${column.className || ""}`}
                      >
                        {column.render
                          ? column.render(item, index)
                          : item[column.key]}
                      </div>
                    ))}
              </div>
            ))
          ) : (
            <div className="table-row empty-row">
              <div className="table-cell full-span empty-message">
                {emptyMessage}
              </div>
            </div>
          )}
        </div>
      ) : (
        // Traditional table layout
        <table className={`table ${className}`}>
          <thead>
            <tr>
              {columns.map((column) => (
                <th key={column.key} className={column.className || ""}>
                  {column.label}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {data.length > 0 ? (
              data.map((item, index) => (
                <tr
                  key={item.id || index}
                  onClick={() => handleRowClick(item)}
                  className={onRowClick ? "clickable" : ""}
                >
                  {renderRow
                    ? renderRow(item, index)
                    : columns.map((column) => (
                        <td key={column.key} className={column.className || ""}>
                          {column.render
                            ? column.render(item, index)
                            : item[column.key]}
                        </td>
                      ))}
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={columns.length} className="empty-message">
                  {emptyMessage}
                </td>
              </tr>
            )}
          </tbody>
        </table>
      )}
    </div>
  );
};

export default Table;
