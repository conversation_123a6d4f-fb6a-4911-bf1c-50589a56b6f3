import React, { useState } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import { FaChevronLeft, FaChevronRight, FaSearchPlus, FaSearchMinus, FaExpand } from 'react-icons/fa';
import '../../styles/CustomPDFViewer.css';

// Set up PDF.js worker with multiple fallback options
try {
  if (import.meta.env.DEV) {
    // In development, try local first, then fallback to CDN
    pdfjs.GlobalWorkerOptions.workerSrc = '/pdf.worker.js';
  } else {
    // In production, use bundled version
    pdfjs.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;
  }
} catch (error) {
  console.warn('PDF worker setup failed, PDFs may not render optimally:', error);
  // If all else fails, disable worker (will use main thread)
  pdfjs.GlobalWorkerOptions.workerSrc = '';
}

const CustomPDFViewer = ({
  fileUrl,
  title = 'PDF Document',
  className = '',
  showDownload = false,
  showPrint = false,
  height = '100%'
}) => {
  const [numPages, setNumPages] = useState(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [scale, setScale] = useState(1.0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [useFallback, setUseFallback] = useState(false);

  const onDocumentLoadSuccess = ({ numPages }) => {
    setNumPages(numPages);
    setLoading(false);
    setError(null);
  };

  const onDocumentLoadError = (error) => {
    console.error('PDF load error:', error);
    console.log('Falling back to iframe viewer...');
    setUseFallback(true);
    setLoading(false);
    setError(null);
  };

  const goToPrevPage = () => {
    setPageNumber(prev => Math.max(1, prev - 1));
  };

  const goToNextPage = () => {
    setPageNumber(prev => Math.min(numPages, prev + 1));
  };

  const zoomIn = () => {
    setScale(prev => Math.min(3.0, prev + 0.2));
  };

  const zoomOut = () => {
    setScale(prev => Math.max(0.5, prev - 0.2));
  };

  const resetZoom = () => {
    setScale(1.0);
  };

  if (error) {
    return (
      <div className={`custom-pdf-viewer ${className}`} style={{ height }}>
        <div className="custom-pdf-viewer__error">
          <p>{error}</p>
          <button
            className="custom-pdf-viewer__retry-btn"
            onClick={() => {
              setError(null);
              setUseFallback(false);
              setLoading(true);
            }}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // If react-pdf fails, use iframe fallback without download/print buttons
  if (useFallback) {
    return (
      <div className={`custom-pdf-viewer ${className}`} style={{ height }}>
        <div className="custom-pdf-viewer__toolbar">
          <div className="custom-pdf-viewer__fallback-info">
            <span>PDF Viewer (Simplified Mode)</span>
          </div>
        </div>
        <div className="custom-pdf-viewer__document">
          <iframe
            src={`${fileUrl}#toolbar=0&navpanes=0&scrollbar=0`}
            style={{
              width: '100%',
              height: '100%',
              border: 'none'
            }}
            title={title}
            onError={() => {
              setError('Failed to load PDF document');
            }}
          />
        </div>
      </div>
    );
  }

  return (
    <div className={`custom-pdf-viewer ${className}`} style={{ height }}>
      {/* Custom Toolbar - Only shows navigation, zoom, and fullscreen */}
      <div className="custom-pdf-viewer__toolbar">
        <div className="custom-pdf-viewer__nav-controls">
          <button
            className="custom-pdf-viewer__btn"
            onClick={goToPrevPage}
            disabled={pageNumber <= 1}
            title="Previous Page"
          >
            <FaChevronLeft />
          </button>

          <span className="custom-pdf-viewer__page-info">
            {loading ? 'Loading...' : `${pageNumber} / ${numPages}`}
          </span>

          <button
            className="custom-pdf-viewer__btn"
            onClick={goToNextPage}
            disabled={pageNumber >= numPages}
            title="Next Page"
          >
            <FaChevronRight />
          </button>
        </div>

        <div className="custom-pdf-viewer__zoom-controls">
          <button
            className="custom-pdf-viewer__btn"
            onClick={zoomOut}
            disabled={scale <= 0.5}
            title="Zoom Out"
          >
            <FaSearchMinus />
          </button>

          <span className="custom-pdf-viewer__zoom-info">
            {Math.round(scale * 100)}%
          </span>

          <button
            className="custom-pdf-viewer__btn"
            onClick={zoomIn}
            disabled={scale >= 3.0}
            title="Zoom In"
          >
            <FaSearchPlus />
          </button>

          <button
            className="custom-pdf-viewer__btn"
            onClick={resetZoom}
            title="Reset Zoom"
          >
            <FaExpand />
          </button>
        </div>

        {/* Conditionally show download and print buttons */}
        {(showDownload || showPrint) && (
          <div className="custom-pdf-viewer__action-controls">
            {showDownload && (
              <button
                className="custom-pdf-viewer__btn custom-pdf-viewer__download-btn"
                onClick={() => {
                  // Download functionality disabled for security
                }}
                title="Download"
              >
                Download
              </button>
            )}

            {showPrint && (
              <button
                className="custom-pdf-viewer__btn custom-pdf-viewer__print-btn"
                onClick={() => {
                  // Print functionality would be handled by parent component
                  window.print();
                }}
                title="Print"
              >
                Print
              </button>
            )}
          </div>
        )}
      </div>

      {/* PDF Document */}
      <div className="custom-pdf-viewer__document">
        {loading && (
          <div className="custom-pdf-viewer__loading">
            <div className="custom-pdf-viewer__spinner"></div>
            <p>Loading PDF...</p>
          </div>
        )}

        <Document
          file={fileUrl}
          onLoadSuccess={onDocumentLoadSuccess}
          onLoadError={onDocumentLoadError}
          loading=""
          error=""
        >
          <Page
            pageNumber={pageNumber}
            scale={scale}
            renderTextLayer={false}
            renderAnnotationLayer={false}
          />
        </Document>
      </div>
    </div>
  );
};

export default CustomPDFViewer;
